import baostock as bs
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def calculate_kdj(data, n=9, m1=3, m2=3):
    """
    计算KDJ指标
    n: RSV计算周期，默认9
    m1: K值平滑周期，默认3
    m2: D值平滑周期，默认3
    """
    # 计算RSV
    low_n = data['low'].rolling(window=n).min()
    high_n = data['high'].rolling(window=n).max()
    rsv = (data['close'] - low_n) / (high_n - low_n) * 100
    
    # 计算K值
    k = rsv.ewm(alpha=1/m1).mean()
    
    # 计算D值
    d = k.ewm(alpha=1/m2).mean()
    
    # 计算J值
    j = 3 * k - 2 * d
    
    return k, d, j

def check_consecutive_yang_lines(data, start_idx, min_consecutive=3):
    """
    检查从指定位置开始是否有连续的阳线
    返回连续阳线的结束位置，如果不满足条件返回-1
    """
    consecutive_count = 0
    for i in range(start_idx, len(data)):
        if data['close'].iloc[i] > data['open'].iloc[i]:  # 阳线
            consecutive_count += 1
            if consecutive_count >= min_consecutive:
                return i  # 返回连续阳线的结束位置
        else:
            if consecutive_count >= min_consecutive:
                return i - 1  # 返回连续阳线的结束位置
            consecutive_count = 0
    
    if consecutive_count >= min_consecutive:
        return len(data) - 1
    return -1

def check_kdj_death_cross(k, d, start_idx):
    """
    检查从指定位置之后是否出现KDJ死叉
    返回死叉位置，如果没有返回-1
    """
    for i in range(start_idx + 1, len(k) - 1):
        if (k.iloc[i-1] >= d.iloc[i-1] and k.iloc[i] < d.iloc[i]):
            return i
    return -1

def analyze_stock(stock_code, days=15):
    """
    分析单只股票是否符合条件
    """
    try:
        # 计算开始日期（多取一些数据以确保有足够的历史数据计算指标）
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=days + 30)).strftime('%Y-%m-%d')
        
        # 查询股票数据
        rs = bs.query_history_k_data_plus(
            stock_code,
            "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
            start_date=start_date, 
            end_date=end_date,
            frequency="d", 
            adjustflag="2"
        )
        
        if rs.error_code != '0':
            print(f"获取股票 {stock_code} 数据失败: {rs.error_msg}")
            return False, None
        
        # 获取数据
        data_list = []
        while rs.next():
            data_list.append(rs.get_row_data())
        
        if len(data_list) < days + 10:  # 确保有足够的数据
            return False, None
            
        data = pd.DataFrame(data_list, columns=rs.fields)
        
        # 数据类型转换
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn']
        for col in numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')
        
        # 只取最近15天的数据进行分析
        data = data.tail(days).reset_index(drop=True)
        
        if len(data) < days:
            return False, None
        
        # 计算KDJ指标
        k, d, j = calculate_kdj(data)
        
        # 检查条件：近15日内出现连续3根阳线后，出现KDJ死叉
        for i in range(len(data) - 6):  # 至少需要留出足够空间给3根阳线和后续的死叉
            # 检查从位置i开始是否有连续3根阳线
            yang_end_idx = check_consecutive_yang_lines(data, i, 3)
            
            if yang_end_idx != -1:
                # 检查阳线结束后是否出现KDJ死叉
                death_cross_idx = check_kdj_death_cross(k, d, yang_end_idx)
                
                if death_cross_idx != -1:
                    # 找到符合条件的股票
                    result_info = {
                        'stock_code': stock_code,
                        'yang_start_date': data['date'].iloc[i],
                        'yang_end_date': data['date'].iloc[yang_end_idx],
                        'death_cross_date': data['date'].iloc[death_cross_idx],
                        'death_cross_k': k.iloc[death_cross_idx],
                        'death_cross_d': d.iloc[death_cross_idx],
                        'current_close': data['close'].iloc[-1]
                    }
                    return True, result_info
        
        return False, None
        
    except Exception as e:
        print(f"分析股票 {stock_code} 时出错: {str(e)}")
        return False, None

def main():
    """
    主函数：读取股票池，筛选符合条件的股票
    """
    # 登录baostock
    lg = bs.login()
    if lg.error_code != '0':
        print(f"登录失败: {lg.error_msg}")
        return
    
    try:
        # 读取股票池文件
        stock_pool_path = r"D:\edge下载\量化文档\stock_pool.xlsx"
        if not os.path.exists(stock_pool_path):
            print(f"股票池文件不存在: {stock_pool_path}")
            return
        
        stock_pool = pd.read_excel(stock_pool_path)
        print(f"读取到股票池，共 {len(stock_pool)} 只股票")
        
        # 假设股票代码在第一列，如果列名不同请修改
        if 'code' in stock_pool.columns:
            stock_codes = stock_pool['code'].tolist()
        elif 'stock_code' in stock_pool.columns:
            stock_codes = stock_pool['stock_code'].tolist()
        else:
            # 如果列名不确定，取第一列
            stock_codes = stock_pool.iloc[:, 0].tolist()
        
        # 筛选结果
        qualified_stocks = []
        
        print("开始筛选股票...")
        for i, stock_code in enumerate(stock_codes):
            if i % 50 == 0:  # 每50只股票显示一次进度
                print(f"正在分析第 {i+1}/{len(stock_codes)} 只股票...")

            # 处理股票代码格式
            original_code = str(stock_code).strip()

            # 如果代码包含.SZ或.SH后缀，转换为baostock格式
            if '.SZ' in original_code.upper():
                stock_code = 'sz.' + original_code.split('.')[0]
            elif '.SH' in original_code.upper():
                stock_code = 'sh.' + original_code.split('.')[0]
            elif not original_code.startswith(('sh.', 'sz.')):
                # 如果没有前缀，根据代码开头判断
                if original_code.startswith('6'):
                    stock_code = 'sh.' + original_code
                elif original_code.startswith(('0', '3')):
                    stock_code = 'sz.' + original_code
                else:
                    stock_code = original_code

            try:
                is_qualified, result_info = analyze_stock(stock_code)

                if is_qualified:
                    qualified_stocks.append(result_info)
                    print(f"  ✓ 符合条件: {stock_code}")
            except Exception as e:
                print(f"  ✗ 分析 {stock_code} 时出错: {str(e)}")
                continue

            # 每100只股票保存一次中间结果
            if (i + 1) % 100 == 0 and qualified_stocks:
                temp_df = pd.DataFrame(qualified_stocks)
                temp_path = r"D:\python\强势股加KDJ趋势策略\temp_qualified_stocks.xlsx"
                temp_df.to_excel(temp_path, index=False)
                print(f"  已保存中间结果，当前找到 {len(qualified_stocks)} 只符合条件的股票")
        
        # 保存结果
        if qualified_stocks:
            result_df = pd.DataFrame(qualified_stocks)
            output_path = r"D:\python\强势股加KDJ趋势策略\qualified_stocks.xlsx"
            result_df.to_excel(output_path, index=False)
            print(f"\n筛选完成！找到 {len(qualified_stocks)} 只符合条件的股票")
            print(f"结果已保存到: {output_path}")
            
            # 显示结果摘要
            print("\n符合条件的股票:")
            for stock in qualified_stocks:
                print(f"股票代码: {stock['stock_code']}")
                print(f"  连续阳线期间: {stock['yang_start_date']} 到 {stock['yang_end_date']}")
                print(f"  KDJ死叉日期: {stock['death_cross_date']}")
                print(f"  死叉时K值: {stock['death_cross_k']:.2f}, D值: {stock['death_cross_d']:.2f}")
                print(f"  当前收盘价: {stock['current_close']:.2f}")
                print("-" * 50)
        else:
            print("\n未找到符合条件的股票")
    
    except Exception as e:
        print(f"程序执行出错: {str(e)}")
    
    finally:
        # 登出baostock
        bs.logout()

if __name__ == "__main__":
    main()
