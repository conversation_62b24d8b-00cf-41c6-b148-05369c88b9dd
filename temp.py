import baostock as bs
#### 登陆系统 ####
lg = bs.login()
start_dates = '2024-07-01'
end_dates = '2024-12-31'
rs = bs.query_history_k_data_plus('sh.000001',"date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",start_date=start_dates, end_date=end_dates,frequency="d", adjustflag="2")
# print('query_history_k_data_plus respond error_code:'+rs.error_code)
# print('query_history_k_data_plus respond  error_msg:'+rs.error_msg)

#### 打印结果集 ####
data_list = []
while (rs.error_code == '0') & rs.next():
    # 获取一条记录，将记录合并在一起
    data_list.append(rs.get_row_data())
result = pd.DataFrame(data_list, columns=rs.fields)
# print(result)
#### 登出系统 ####
bs.logout()

# 替换您现有的数据类型转换代码
numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn']
for col in numeric_columns:
    if col in result.columns:
        result[col] = pd.to_numeric(result[col], errors='coerce')

stock_data = result
chan = Chan_Strategy()
#stock_data = ak.stock_zh_a_hist(symbol = '000001',period = "daily",start_date = '20240321',end_date = '20240330',adjust = "")
for i in range(len(stock_data)):
    bar = BarData(stock_data['date'][i], stock_data['high'][i], stock_data['low'][i], volume=0, open_interest=0, close_price=stock_data['close'][i], open_price=stock_data['open'][i],gap=0)
    chan.on_bar(bar)
print(bar.high_price)
print(bar.low_price)
print(bar.open_price)
print(bar.close_price)