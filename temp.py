import baostock as bs
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
#### 登陆系统 ####
lg = bs.login()
start_dates = '2025-04-01'
end_dates = '2025-07-02'
rs = bs.query_history_k_data_plus('sz.002235',"date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",start_date=start_dates, end_date=end_dates,frequency="d", adjustflag="2")
# print('query_history_k_data_plus respond error_code:'+rs.error_code)
# print('query_history_k_data_plus respond  error_msg:'+rs.error_msg)

#### 打印结果集 ####
data_list = []
while (rs.error_code == '0') & rs.next():
    # 获取一条记录，将记录合并在一起
    data_list.append(rs.get_row_data())
result = pd.DataFrame(data_list, columns=rs.fields)
# print(result)
#### 登出系统 ####
bs.logout()

# 替换您现有的数据类型转换代码
numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'turn']
for col in numeric_columns:
    if col in result.columns:
        result[col] = pd.to_numeric(result[col], errors='coerce')

stock_data = result

def calculate_kdj(data, n=9, m1=3, m2=3):
    """
    计算KDJ指标
    n: RSV计算周期，默认9
    m1: K值平滑周期，默认3
    m2: D值平滑周期，默认3
    """
    # 计算RSV
    low_n = data['low'].rolling(window=n).min()
    high_n = data['high'].rolling(window=n).max()
    rsv = (data['close'] - low_n) / (high_n - low_n) * 100

    # 计算K值
    k = rsv.ewm(alpha=1/m1).mean()

    # 计算D值
    d = k.ewm(alpha=1/m2).mean()

    # 计算J值
    j = 3 * k - 2 * d

    return k, d, j

def calculate_ema(data, period):
    """
    计算EMA指标
    data: 价格数据
    period: 计算周期
    """
    return data.ewm(span=period).mean()

# 设置中文字体和负号显示
def set_chinese_font():
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    
def plot_kline_with_indicators(data):
    """
    绘制K线图和技术指标
    """
    set_chinese_font()
    # 转换日期格式
    data['date'] = pd.to_datetime(data['date'])
    data = data.set_index('date')

    # 计算技术指标
    k, d, j = calculate_kdj(data)
    ema12 = calculate_ema(data['close'], 12)
    ema26 = calculate_ema(data['close'], 26)

    # 创建子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)

    # 绘制K线图
    for i in range(len(data)):
        color = 'red' if data['close'].iloc[i] >= data['open'].iloc[i] else 'green'
        # 绘制实体
        ax1.plot([i, i], [data['open'].iloc[i], data['close'].iloc[i]],
                color=color, linewidth=3)
        # 绘制影线
        ax1.plot([i, i], [data['low'].iloc[i], data['high'].iloc[i]],
                color=color, linewidth=1)

    # 绘制EMA
    ax1.plot(range(len(data)), ema12, label='EMA12', color='blue', linewidth=1)
    ax1.plot(range(len(data)), ema26, label='EMA26', color='orange', linewidth=1)

    ax1.set_title('K线图与EMA指标')
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 绘制KDJ指标
    ax2.plot(range(len(data)), k, label='K', color='blue', linewidth=1)
    ax2.plot(range(len(data)), d, label='D', color='red', linewidth=1)
    ax2.plot(range(len(data)), j, label='J', color='green', linewidth=1)

    # 添加超买超卖线
    ax2.axhline(y=80, color='red', linestyle='--', alpha=0.5, label='超买线(80)')
    ax2.axhline(y=20, color='green', linestyle='--', alpha=0.5, label='超卖线(20)')

    ax2.set_title('KDJ指标')
    ax2.set_ylabel('KDJ值')
    ax2.set_xlabel('时间')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 设置x轴标签
    x_ticks = range(0, len(data), max(1, len(data)//10))
    x_labels = [data.index[i].strftime('%Y-%m-%d') for i in x_ticks]
    ax2.set_xticks(x_ticks)
    ax2.set_xticklabels(x_labels, rotation=45)

    plt.tight_layout()
    plt.show()

    return k, d, j, ema12, ema26

# 计算并绘制指标
print("正在计算技术指标并绘制图表...")
k, d, j, ema12, ema26 = plot_kline_with_indicators(stock_data)

print(stock_data)

# 显示最新的指标值
print(f"\n最新指标值:")
print(f"K值: {k.iloc[-1]:.2f}")
print(f"D值: {d.iloc[-1]:.2f}")
print(f"J值: {j.iloc[-1]:.2f}")
print(f"EMA12: {ema12.iloc[-1]:.2f}")
print(f"EMA26: {ema26.iloc[-1]:.2f}")

# 简单的交易信号判断
if k.iloc[-1] > d.iloc[-1] and k.iloc[-2] <= d.iloc[-2]:
    print("\n交易信号: KDJ金叉，可能的买入信号")
elif k.iloc[-1] < d.iloc[-1] and k.iloc[-2] >= d.iloc[-2]:
    print("\n交易信号: KDJ死叉，可能的卖出信号")

if ema12.iloc[-1] > ema26.iloc[-1]:
    print("EMA信号: EMA12在EMA26之上，趋势向上")
else:
    print("EMA信号: EMA12在EMA26之下，趋势向下")