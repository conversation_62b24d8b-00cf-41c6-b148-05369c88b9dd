#encoding:gbk
'''
本策略事先设定好交易的股票篮子，然后根据指数的CCI指标来判断超买和超卖
当有超买和超卖发生时，交易事先设定好的股票篮子
'''
import pandas as pd
import numpy as np
import talib
from collections import defaultdict, deque
import datetime

def stockcode_format(code):
    code = code[:6]
    code = code.zfill(6)  # 确保股票代码为6位
    # print(f"正在处理股票: {code}")
    # 根据股票代码判断是上交所还是深交所
    if code.startswith('6'):
        full_code = f"{code}.SH"  # 上交所股票代码
    elif code.startswith('688'):
        full_code = f"{code}.SH"  # 科创板股票代码
    elif code.startswith('3'):
        full_code = f"{code}.SZ"  # 创业板股票代码
    elif code.startswith('0'):
        full_code = f"{code}.SZ"  # 深交所主板股票代码
    else:
        full_code = f"{code}.SZ"  # 默认使用深交所 
    return full_code 
    
def init(ContextInfo):
    #hs300成分股中sh和sz市场各自流通市值最大的前3只股票
    df = pd.read_excel('D:\\python\\强势股加KDJ趋势策略\\temp_qualified_stocks.xlsx')
    stock_codelist = df["stock_code"].tolist()
    stock_codelist = ['600113.SH']
    ContextInfo.trade_code_list=stock_codelist
    ContextInfo.set_universe(ContextInfo.trade_code_list)
    ContextInfo.accID = '620000105985'
    ContextInfo.nums = 1000
    ContextInfo.buy = True
    ContextInfo.sell = True
    ContextInfo.stock_tick_sum = []

    start_time_tick = "20250718"
    tickdata = ContextInfo.get_market_data_ex(['open', 'high', 'low', 'close', 'volume'],ContextInfo.trade_code_list,period = 'tick', start_time = start_time_tick, end_time = '',count=5, dividend_type='follow', fill_data=True, subscribe=True)
    #print("tickdata")
    #print(tickdata)
    # 实时tick数据
    stock_tick_info=ContextInfo.get_full_tick(ContextInfo.trade_code_list)
    #print("stock_tick_info")
    #print(stock_tick_info[stock_codelist[0]]['volume'])

def handlebar(ContextInfo):
    #计算当前主图的cci
    #if not ContextInfo.is_last_bar():
    #    return  
    start_time_tick = "20250718"
    tickdata = ContextInfo.get_market_data_ex(['open', 'high', 'low', 'close', 'volume'],ContextInfo.trade_code_list,period = 'tick', start_time = start_time_tick, end_time = '',count=5, dividend_type='follow', fill_data=True, subscribe=True)
    print("tickdata")
    print(tickdata)
    # 实时tick数据
    stock_tick_info=ContextInfo.get_full_tick(ContextInfo.trade_code_list)
    ContextInfo.stock_tick_sum.append(stock_tick_info[ContextInfo.trade_code_list[0]]['volume'])

    # 获取当前时间（假设ContextInfo.current_time为'YYYY-MM-DD HH:MM:SS'格式字符串）
    try:
        now = ContextInfo.current_time
        if isinstance(now, str):
            now = datetime.datetime.strptime(now, "%Y-%m-%d %H:%M:%S")
    except:
        now = datetime.datetime.now()

    # 判断是否为14:58
    if now.hour == 14 and now.minute == 58:
        # 保存stock_tick_sum
        pd.DataFrame(ContextInfo.stock_tick_sum, columns=['volume']).to_excel("D:\\python\\强势股加KDJ趋势策略\\stock_tick_sum.xlsx", index=False)
        # 保存tickdata
        # tickdata 可能是字典或DataFrame，需根据实际结构调整
        if isinstance(tickdata, dict):
            for code, df in tickdata.items():
                pd.DataFrame(df).to_excel(f"D:\\python\\强势股加KDJ趋势策略\\tickdata_{code}.xlsx", index=False)
        elif isinstance(tickdata, pd.DataFrame):
            tickdata.to_excel("D:\\python\\强势股加KDJ趋势策略\\tickdata.xlsx", index=False)
        print("已保存stock_tick_sum和tickdata到Excel")
    #print(stock_tick_info[stock_codelist[0]]['volume'])
    #print(stock_tick_info)
    #ContextInfo.paint('can_sell',ContextInfo.sell,-1,0,'nodraw')





